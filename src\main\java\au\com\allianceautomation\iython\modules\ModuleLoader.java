package au.com.allianceautomation.iython.modules;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import au.com.allianceautomation.iython.exceptions.ModuleNotFoundError;

/**
 * Handles module loading and caching for the iython interpreter.
 * Supports loading modules from the Python standard library directory.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class ModuleLoader {
    private static final Logger logger = LoggerFactory.getLogger(ModuleLoader.class);
    
    // Python library directory relative to resources
    private static final String PYTHON_LIB_DIR = "src/main/resources/Libs/Python-3.12.8";
    
    // Module cache (equivalent to sys.modules in Python)
    private final Map<String, PythonModule> moduleCache = new HashMap<>();
    
    /**
     * Load a module by name.
     * 
     * @param moduleName The name of the module to load
     * @return The loaded module
     * @throws PythonRuntimeException if the module cannot be found or loaded
     */
    public PythonModule loadModule(String moduleName) {
        // Check if module is already cached
        if (moduleCache.containsKey(moduleName)) {
            return moduleCache.get(moduleName);
        }
        
        // Try to find and load the module
        PythonModule module = findAndLoadModule(moduleName);
        
        if (module == null) {
            throw new ModuleNotFoundError(moduleName);
        }
        
        // Cache the module
        moduleCache.put(moduleName, module);
        return module;
    }
    
    /**
     * Find and load a module from the file system.
     * 
     * @param moduleName The name of the module to find
     * @return The loaded module or null if not found
     */
    private PythonModule findAndLoadModule(String moduleName) {
        // Try to find the module file
        Path modulePath = findModuleFile(moduleName);
        
        if (modulePath == null) {
            return null;
        }
        
        try {
            // Read the module source code
            String sourceCode = Files.readString(modulePath);
            
            // Create and return the module
            return new PythonModule(moduleName, modulePath.toString(), sourceCode);
            
        } catch (IOException e) {
            logger.error("Failed to read module file: " + modulePath, e);
            return null;
        }
    }
    
    /**
     * Find the file for a given module name.
     * 
     * @param moduleName The name of the module to find
     * @return The path to the module file or null if not found
     */
    private Path findModuleFile(String moduleName) {
        // Convert module name to file path (handle dotted names)
        String[] parts = moduleName.split("\\.");
        
        // Start with the Python library directory
        Path basePath = Paths.get(PYTHON_LIB_DIR);
        
        // Build the path for the module
        Path currentPath = basePath;
        for (int i = 0; i < parts.length - 1; i++) {
            currentPath = currentPath.resolve(parts[i]);
        }
        
        String lastPart = parts[parts.length - 1];
        
        // Try different file extensions and patterns
        Path[] candidates = {
            currentPath.resolve(lastPart + ".py"),           // module.py
            currentPath.resolve(lastPart).resolve("__init__.py"), // package/__init__.py
        };
        
        for (Path candidate : candidates) {
            if (Files.exists(candidate) && Files.isRegularFile(candidate)) {
                logger.debug("Found module {} at {}", moduleName, candidate);
                return candidate;
            }
        }
        
        logger.debug("Module {} not found in {}", moduleName, basePath);
        return null;
    }
    
    /**
     * Check if a module exists without loading it.
     * 
     * @param moduleName The name of the module to check
     * @return true if the module exists, false otherwise
     */
    public boolean moduleExists(String moduleName) {
        return moduleCache.containsKey(moduleName) || findModuleFile(moduleName) != null;
    }
    
    /**
     * Get a cached module without loading it.
     * 
     * @param moduleName The name of the module
     * @return The cached module or null if not cached
     */
    public PythonModule getCachedModule(String moduleName) {
        return moduleCache.get(moduleName);
    }
    
    /**
     * Clear the module cache.
     */
    public void clearCache() {
        moduleCache.clear();
    }
    
    /**
     * Get all cached module names.
     * 
     * @return A map of all cached modules
     */
    public Map<String, PythonModule> getAllCachedModules() {
        return new HashMap<>(moduleCache);
    }
}
